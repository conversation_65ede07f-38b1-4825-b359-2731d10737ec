package inks.service.sa.mqs.auth;

import org.dromara.mica.mqtt.core.server.auth.IMqttServerAuthHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.tio.core.ChannelContext;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 基于 Token 的 MQTT 认证处理器
 * 用户名可直接作为 Token，向后兼容用户名密码方式
 */
@Component
public class TokenBasedMqttAuthHandler implements IMqttServerAuthHandler {

    private static final Logger logger = LoggerFactory.getLogger(TokenBasedMqttAuthHandler.class);

    /** 模拟的有效 Token 列表（生产环境请使用 DB/缓存） */
    private static final Set<String> VALID_TOKENS = new HashSet<>();

    static {
        VALID_TOKENS.add("device_token_001");
        VALID_TOKENS.add("device_token_002");
        VALID_TOKENS.add("sensor_abc123");
        VALID_TOKENS.add("iphone_token_xyz");
        VALID_TOKENS.add("inks");               // 兼容传统方式
    }

    public TokenBasedMqttAuthHandler() {
        logger.info("=== TokenBasedMqttAuthHandler 已创建并注册 ===");
    }

    @Override
    public boolean authenticate(ChannelContext context,
                                String uniqueId,
                                String clientId,
                                String userName,
                                String password) {

        String ip = context.getClientNode() != null ? context.getClientNode().getIp() : "unknown";

        // t-io 自带的线程安全 attr 存储:contentReference[oaicite:0]{index=0}
        logger.info("MQTT 认证请求 - uniqueId:{}, clientId:{}, userName:{}, ip:{}",
                uniqueId, clientId, userName, ip);

        // 1. 用户名不能为空
        if (userName == null || userName.trim().isEmpty()) {
            logger.warn("认证失败：用户名为空 - clientId:{}", clientId);
            return false;
        }

        // 2. Token 认证
        if (VALID_TOKENS.contains(userName)) {
            logger.info("Token=username认证成功 - clientId:{}, token:{}", clientId, userName);
            // 认证通过后把用户名（token）塞进 ChannelContext
            context.set("username", userName);
            return true;
        }

        // 3. 兼容用户名密码
        if (Objects.equals("inks", userName) && Objects.equals("8866", password)) {
            logger.info("用户名密码认证成功 - clientId:{}, userName:{}", clientId, userName);
            return true;
        }

        logger.warn("认证失败 - clientId:{}, userName:{}", clientId, userName);
        return false;
    }

    /* ===== 辅助方法：动态管理 Token ===== */

    public static void addToken(String token) {
        VALID_TOKENS.add(token);
        logger.info("添加新 Token: {}", token);
    }

    public static void removeToken(String token) {
        VALID_TOKENS.remove(token);
        logger.info("移除 Token: {}", token);
    }

    public static Set<String> getAllTokens() {
        return new HashSet<>(VALID_TOKENS);
    }
}
