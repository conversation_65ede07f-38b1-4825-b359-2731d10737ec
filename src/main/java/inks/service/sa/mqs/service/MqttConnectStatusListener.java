package inks.service.sa.mqs.service;

import org.dromara.mica.mqtt.spring.server.event.MqttClientOfflineEvent;
import org.dromara.mica.mqtt.spring.server.event.MqttClientOnlineEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@Service
public class MqttConnectStatusListener {
    private static final Logger logger = LoggerFactory.getLogger(MqttConnectStatusListener.class);

    @EventListener
    public void online(MqttClientOnlineEvent event) {
        logger.info("MqttClientOnlineEvent:{}", event);
    }

    @EventListener
    public void offline(MqttClientOfflineEvent event) {
        logger.info("MqttClientOfflineEvent:{}", event);
    }

}