package inks.service.sa.mqs.service;

import inks.service.sa.mqs.auth.TokenBasedMqttAuthHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.UUID;

/**
 * 设备Token管理服务
 * 用于管理MQTT设备的访问Token
 * 
 * <AUTHOR>
 */
@Service
public class DeviceTokenService {
    
    private static final Logger logger = LoggerFactory.getLogger(DeviceTokenService.class);
    
    /**
     * 生成新的设备Token
     * @param deviceId 设备ID
     * @param deviceType 设备类型（如：sensor, iphone, android等）
     * @return 生成的Token
     */
    public String generateDeviceToken(String deviceId, String deviceType) {
        // 生成格式：deviceType_deviceId_randomUUID
        String token = String.format("%s_%s_%s", 
                                   deviceType, 
                                   deviceId, 
                                   UUID.randomUUID().toString().replace("-", "").substring(0, 8));
        
        // 添加到有效Token列表
        TokenBasedMqttAuthHandler.addToken(token);
        
        logger.info("为设备生成新Token - DeviceId: {}, DeviceType: {}, Token: {}", 
                   deviceId, deviceType, token);
        
        return token;
    }
    
    /**
     * 添加预定义Token
     * @param token Token字符串
     * @return 是否添加成功
     */
    public boolean addToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            logger.warn("尝试添加空Token");
            return false;
        }
        
        TokenBasedMqttAuthHandler.addToken(token.trim());
        logger.info("添加Token成功: {}", token);
        return true;
    }
    
    /**
     * 移除Token
     * @param token 要移除的Token
     * @return 是否移除成功
     */
    public boolean removeToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            logger.warn("尝试移除空Token");
            return false;
        }
        
        TokenBasedMqttAuthHandler.removeToken(token.trim());
        logger.info("移除Token成功: {}", token);
        return true;
    }
    
    /**
     * 获取所有有效Token
     * @return Token集合
     */
    public Set<String> getAllTokens() {
        return TokenBasedMqttAuthHandler.getAllTokens();
    }
    
    /**
     * 验证Token是否有效
     * @param token 要验证的Token
     * @return 是否有效
     */
    public boolean isValidToken(String token) {
        return TokenBasedMqttAuthHandler.getAllTokens().contains(token);
    }
    
    /**
     * 批量添加测试Token
     */
    public void initTestTokens() {
        // iPhone设备Token
        addToken("iphone_apple_a1297_token");
        addToken("iphone_12_pro_token");
        
        // Android设备Token  
        addToken("android_samsung_s21_token");
        addToken("android_xiaomi_mi11_token");
        
        // 传感器Token
        addToken("sensor_temperature_001");
        addToken("sensor_humidity_002");
        addToken("sensor_pressure_003");
        
        // IoT设备Token
        addToken("iot_gateway_main");
        addToken("iot_camera_front");
        addToken("iot_light_bedroom");
        
        logger.info("初始化测试Token完成，共添加了9个测试Token");
    }
}
