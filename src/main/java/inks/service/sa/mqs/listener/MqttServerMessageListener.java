package inks.service.sa.mqs.listener;

import org.dromara.mica.mqtt.codec.MqttPublishMessage;
import org.dromara.mica.mqtt.codec.MqttQoS;
import org.dromara.mica.mqtt.core.server.event.IMqttMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.tio.core.ChannelContext;

import java.nio.charset.StandardCharsets;

//  2.2 可实现接口（注册成 Spring Bean 即可） https://gitee.com/dromara/mica-mqtt/blob/master/starter/mica-mqtt-server-spring-boot-starter/README.md#
//  IMqttMessageListener	否（1.3.x为否）	消息监听
@Service
public class MqttServerMessageListener implements IMqttMessageListener {
    private static final Logger logger = LoggerFactory.getLogger(MqttServerMessageListener.class);

    @Override
    public void onMessage(ChannelContext context, String clientId, String topic, MqttQoS qoS, MqttPublishMessage message) {
        // 取回当初存的用户名 / Token
        String userName = (String) context.get("username");
        logger.info("clientId:{}, userName:{}, topic:{}, payload:{}",
                clientId, userName, topic,
                new String(message.getPayload(), StandardCharsets.UTF_8));
    }
}
