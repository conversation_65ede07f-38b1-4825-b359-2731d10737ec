package inks.service.sa.mqs;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.net.InetAddress;
import java.net.UnknownHostException;

@SpringBootApplication(scanBasePackages = {"inks.service.sa.mqs"})
@EnableSwagger2
public class InksServiceSaMqsApplication {

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(InksServiceSaMqsApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String property = env.getProperty("spring.application.name");
        String path = property == null ? "" : property;
        System.out.println(
                "\n\t" +
                        "----------------------------------------------------------\n\t" +
                        "Application Sailrui-Boot is running! Access URLs:\n\t" +
                        "Local: \t\thttp://localhost:" + port + "/swagger-ui.html\n\t" +
                        "Local: \t\thttp://localhost:" + port + "\n\t" +
                        "External: \thttp://" + ip + ":" + port + "/swagger-ui.html\n\t" +
                        "Nacos: \t\thttp://dev.inksyun.com:" + port + "/swagger-ui.html\n\t" +
                        "------------------------------------------------------------");
    }

}
