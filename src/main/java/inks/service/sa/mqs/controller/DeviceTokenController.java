package inks.service.sa.mqs.controller;

import inks.service.sa.mqs.service.DeviceTokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 设备Token管理API
 * 提供Token的创建、查询、删除等功能
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/device-tokens")
public class DeviceTokenController {
    
    @Autowired
    private DeviceTokenService deviceTokenService;
    
    /**
     * 获取所有Token
     */
    @GetMapping
    public ResponseEntity<Set<String>> getAllTokens() {
        return ResponseEntity.ok(deviceTokenService.getAllTokens());
    }
    
    /**
     * 生成新Token
     */
    @PostMapping("/generate")
    public ResponseEntity<Map<String, String>> generateToken(
            @RequestParam String deviceId,
            @RequestParam String deviceType) {
        
        String token = deviceTokenService.generateDeviceToken(deviceId, deviceType);
        
        Map<String, String> response = new HashMap<>();
        response.put("token", token);
        response.put("deviceId", deviceId);
        response.put("deviceType", deviceType);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 添加自定义Token
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> addToken(@RequestParam String token) {
        boolean success = deviceTokenService.addToken(token);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        response.put("token", token);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 删除Token
     */
    @DeleteMapping
    public ResponseEntity<Map<String, Object>> removeToken(@RequestParam String token) {
        boolean success = deviceTokenService.removeToken(token);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        response.put("token", token);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 验证Token
     */
    @GetMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateToken(@RequestParam String token) {
        boolean isValid = deviceTokenService.isValidToken(token);
        
        Map<String, Object> response = new HashMap<>();
        response.put("valid", isValid);
        response.put("token", token);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 初始化测试Token
     */
    @PostMapping("/init-test-tokens")
    public ResponseEntity<Map<String, Object>> initTestTokens() {
        deviceTokenService.initTestTokens();
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "测试Token初始化成功");
        response.put("tokens", deviceTokenService.getAllTokens());
        
        return ResponseEntity.ok(response);
    }
}
