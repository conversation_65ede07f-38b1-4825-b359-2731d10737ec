package inks.service.sa.mqs.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("A_Hello")
@Api(tags = "A_Hello 测试开启项目*_*")
public class A_HelloController {


    @GetMapping
    public String hello() {
        return "Hello World!";
    }
}
