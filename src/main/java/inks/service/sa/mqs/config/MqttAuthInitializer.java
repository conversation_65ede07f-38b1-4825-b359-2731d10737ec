package inks.service.sa.mqs.config;

import inks.service.sa.mqs.service.DeviceTokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * MQTT认证初始化器
 * 在应用启动时初始化测试Token
 * 
 * <AUTHOR>
 */
@Component
public class MqttAuthInitializer implements ApplicationRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(MqttAuthInitializer.class);
    
    @Autowired
    private DeviceTokenService deviceTokenService;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("开始初始化MQTT认证配置...");
        
        // 初始化测试Token
        deviceTokenService.initTestTokens();
        
        logger.info("MQTT认证配置初始化完成");
        logger.info("当前有效Token数量: {}", deviceTokenService.getAllTokens().size());
        
        // 打印使用说明
        printUsageInstructions();
    }
    
    private void printUsageInstructions() {
        logger.info("=== MQTT Token认证使用说明 ===");
        logger.info("1. MQTT客户端连接时，将Token作为用户名");
        logger.info("2. 密码可以为空或任意值（不会被验证）");
        logger.info("3. 支持的连接方式：");
        logger.info("   - 仅用户名：mosquitto_pub -h localhost -p 1883 -u 'your_token' -t 'test/topic' -m 'hello'");
        logger.info("   - 用户名+空密码：mosquitto_pub -h localhost -p 1883 -u 'your_token' -P '' -t 'test/topic' -m 'hello'");
        logger.info("4. 管理API：");
        logger.info("   - 查看所有Token: GET /api/device-tokens");
        logger.info("   - 添加Token: POST /api/device-tokens?token=your_token");
        logger.info("   - 删除Token: DELETE /api/device-tokens?token=your_token");
        logger.info("   - 生成Token: POST /api/device-tokens/generate?deviceId=xxx&deviceType=xxx");
        logger.info("5. 当前可用的测试Token:");
        deviceTokenService.getAllTokens().forEach(token -> 
            logger.info("   - {}", token));
        logger.info("=== 使用说明结束 ===");
    }
}
