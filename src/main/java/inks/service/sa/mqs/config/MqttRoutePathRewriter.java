package inks.service.sa.mqs.config;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.lang.reflect.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class MqttRoutePathRewriter implements ApplicationRunner {

    @Override
    public void run(ApplicationArguments args) throws Exception {
        Class<?> routesClass = Class.forName("org.dromara.mica.mqtt.core.server.http.handler.MqttHttpRoutes");
        Class<?> methodClass = Class.forName("org.tio.http.common.Method");
        Class<?> handlerClass = Class.forName("org.dromara.mica.mqtt.core.server.http.handler.HttpHandler");
        Class<?> requestClass = Class.forName("org.tio.http.common.HttpRequest");
        Class<?> responseClass = Class.forName("org.tio.http.common.HttpResponse");

        Field routesField = routesClass.getDeclaredField("ROUTS");
        routesField.setAccessible(true);
        Map<Object, Object> routes = (Map<Object, Object>) routesField.get(null);

        List<Map.Entry<Object, Object>> entries = new ArrayList<>(routes.entrySet());
        for (Map.Entry<Object, Object> entry : entries) {
            Object routeInfo = entry.getKey();

            Field pathField = routeInfo.getClass().getDeclaredField("path");
            pathField.setAccessible(true);
            String originalPath = (String) pathField.get(routeInfo);

            Field methodField = routeInfo.getClass().getDeclaredField("method");
            methodField.setAccessible(true);
            Object methodEnum = methodField.get(routeInfo);

            String newPath = originalPath.replace("/api/v1/", "/mqs/v1/");
            if (!originalPath.equals(newPath)) {
                Object originalHandler = entry.getValue();
                // 创建代理处理器，添加CORS头
                Object proxyHandler = Proxy.newProxyInstance(
                        handlerClass.getClassLoader(),
                        new Class<?>[]{handlerClass},
                        new CorsHandlerInvoker(originalHandler, requestClass, responseClass)
                );

                Method registerMethod = routesClass.getMethod("register", methodClass, String.class, handlerClass);
                registerMethod.invoke(null, methodEnum, newPath, proxyHandler);
            }
        }
    }

    // 自定义InvocationHandler添加CORS头
    private static class CorsHandlerInvoker implements InvocationHandler {
        private final Object targetHandler;
        private final Class<?> requestClass;
        private final Class<?> responseClass;

        public CorsHandlerInvoker(Object targetHandler, Class<?> requestClass, Class<?> responseClass) {
            this.targetHandler = targetHandler;
            this.requestClass = requestClass;
            this.responseClass = responseClass;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            // 调用原始处理器的处理方法
            Object result = method.invoke(targetHandler, args);
            
            // 遍历参数找到HttpResponse对象
            for (Object arg : args) {
                if (responseClass.isInstance(arg)) {
                    // 添加CORS响应头
                    Method addHeader = responseClass.getMethod("addHeader", String.class, String.class);
                    addHeader.invoke(arg, "Access-Control-Allow-Origin", "*");
                    addHeader.invoke(arg, "Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
                    addHeader.invoke(arg, "Access-Control-Allow-Headers", "*");
                    addHeader.invoke(arg, "Access-Control-Max-Age", "1800");
                    break;
                }
            }
            return result;
        }
    }
}