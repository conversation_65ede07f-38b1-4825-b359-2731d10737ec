server:
  port: 10686 #服务端口
spring:
  application:
    name: sa-mqs
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
# 开启 redis stream
mica:
  redis:
    stream:
      enable: true
# actuator management
management:
  info:
    defaults:
      enabled: true
  metrics:
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      show-details: ALWAYS
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: '*'
# 日志配置
logging:
  level:
    root: info
    server: info # t-io 服务端默认日志
    org.tio: info # t-io 服务端默认日志